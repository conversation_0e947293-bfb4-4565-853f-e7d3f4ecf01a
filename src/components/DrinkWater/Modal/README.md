# 喝水模块弹窗组件

## 功能概述
基于红包雨模块的弹窗组件，为喝水模块提供奖励告知弹窗和膨胀弹窗功能。

## 使用场景

### 1. 正常打卡
- 如果 `rewardGoldCoin` 返回的 `upgradeCoins` 字段不为0，显示膨胀弹窗（AdRewardModalContent）
- 否则显示普通奖励弹窗（RewardModalContent）

### 2. 连续7天打卡&满勤
- 在发放金币成功后，直接显示奖励告知弹窗（RewardModalContent）

## 新增功能

### 关闭按钮 ✅
参照ListenTask模块下的ListenTaskModal，在弹窗右上角添加了关闭按钮：
- 位置：右上角，距离右边16px，距离顶部44px
- 样式：32x32px的触摸区域，内含16px的白色关闭图标
- 功能：点击关闭弹窗，触发埋点上报

## 组件属性

```typescript
interface DrinkWaterModalProps {
  visible: boolean;           // 弹窗是否可见
  onClose: () => void;        // 关闭弹窗回调
  onPress?: () => void;       // 广告按钮点击回调（仅膨胀弹窗）
  children?: React.ReactNode; // 子组件
  coins?: number;             // 金币数量
  type?: 'withAd' | 'normal'; // 弹窗类型
  upgradeCoins?: number;      // 升级金币数量（仅膨胀弹窗）
  title?: string;             // 弹窗标题
  subTitle?: string;          // 副标题
}
```

## 使用示例

### 普通奖励弹窗
```tsx
<DrinkWaterModal
  visible={modalVisible}
  onClose={() => setModalVisible(false)}
  coins={50}
  type="normal"
  title="恭喜获得"
/>
```

### 膨胀弹窗
```tsx
<DrinkWaterModal
  visible={modalVisible}
  onClose={() => setModalVisible(false)}
  onPress={handleAdReward}
  coins={50}
  type="withAd"
  upgradeCoins={100}
  title="恭喜获得"
/>
```

## 复用的组件
- `RewardModalContent`: 普通奖励弹窗内容
- `AdRewardModalContent`: 膨胀弹窗内容
- `ConfettiAnimation`: 彩带动画效果

## 埋点上报
- 弹窗展示：`xmlog.event(68281, 'dialogView')`
- 按钮点击：`xmlog.event(68282, 'dialogClick')`
- 页面标识：`currPage: 'drinkWater'`
- 来源标识：`from: '喝水打卡'`
