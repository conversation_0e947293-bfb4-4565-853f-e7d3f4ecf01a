import { StyleSheet } from 'react-native';
import { px } from 'utils/px';

export const getStyles = () => {
  return StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: px(16),
      paddingTop: px(54), // 状态栏高度
      paddingBottom: px(10),
      backgroundColor: '#FFFFFF',
    },
    backButton: {
      width: px(44),
      height: px(44),
      alignItems: 'center',
      justifyContent: 'center',
    },
    backIcon: {
      width: px(24),
      height: px(24),
    },
    title: {
      fontSize: px(17),
      fontWeight: '600',
      color: '#000000',
      textAlign: 'center',
    },
    placeholder: {
      width: px(44),
      height: px(44),
    },
  });
};
