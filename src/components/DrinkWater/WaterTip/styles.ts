import { StyleSheet } from 'react-native';
import { px } from 'utils/px';

export const getStyles = () => {
  return StyleSheet.create({
    container: {
      backgroundColor: '#FFFFFF',
      marginHorizontal: px(16),
      marginTop: px(20),
      borderRadius: px(4),
      overflow: 'hidden',
    },
    backgroundIcon: {
      position: 'absolute',
      right: 0,
      width: px(84),
      height: px(88),
      zIndex: 1,
    },
    textContainer: {
      width: '100%',
      paddingHorizontal: px(16),
      paddingVertical: px(20),
      zIndex: 2,
      position: 'relative',
    },
    title: {
      fontSize: px(16),
      fontWeight: '600',
      color: '#131415',
      marginBottom: px(8),
      width: '100%',
    },
    subTitle: {
      fontSize: px(13),
      color: '#999999',
      width: '100%',
    },
  });
};
