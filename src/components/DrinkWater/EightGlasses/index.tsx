import React, { useEffect } from 'react';
import { View, Text, Image } from 'react-native';
import { useAtomValue, useSet<PERSON>tom } from 'jotai';
import { getStyles } from './styles';
import { waterInfoAtom, writeWaterInfoAtom, waterInfoLoadingAtom } from './store';
import WaterGlass from './WaterGlass';

// 导入小猫图片
const catImage = require('../../../imagesV2/drink_water_cat.png');

export default function EightGlasses() {
  const styles = getStyles();
  const waterInfo = useAtomValue(waterInfoAtom);
  const loading = useAtomValue(waterInfoLoadingAtom);
  const fetchWaterInfo = useSetAtom(writeWaterInfoAtom);

  useEffect(() => {
    fetchWaterInfo();
  }, []);

  // if (loading) {
  //   return (
  //     <View style={styles.container}>
  //       <View style={styles.loadingContainer}>
  //         <Text style={styles.loadingText}>加载中...</Text>
  //       </View>
  //     </View>
  //   );
  // }

  if (!waterInfo?.waterInfo) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>暂无数据</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Image source={catImage} style={styles.catImage} />
      <View style={styles.glassesContainer}>
        <View style={styles.row}>
          {waterInfo.waterInfo.slice(0, 4).map((water, index) => (
            <View key={water.index} style={[styles.glassWrapper, index < 3 && styles.glassMarginRight]}>
              <WaterGlass
                waterInfo={water}
                onRefresh={fetchWaterInfo}
              />
            </View>
          ))}
        </View>

        <View style={[styles.row, styles.lastRow]}>
          {waterInfo.waterInfo.slice(4, 8).map((water, index) => (
            <View key={water.index} style={[styles.glassWrapper, index < 3 && styles.glassMarginRight]}>
              <WaterGlass
                waterInfo={water}
                onRefresh={fetchWaterInfo}
              />
            </View>
          ))}
        </View>
      </View>
    </View>
  );
}
