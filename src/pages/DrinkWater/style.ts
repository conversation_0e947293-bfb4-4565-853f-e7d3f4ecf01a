import { StyleSheet } from 'react-native';
import { px } from 'utils/px';
import { Dimensions } from 'react-native';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export const getStyles = () => {
  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#E5F2FF',
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      paddingBottom: px(67),
      position: 'relative',
    },
    contentWrapper: {
      position: 'relative',
      zIndex: 2,
    },
    backgroundImage: {
      position: 'absolute',
      top: 0,
      left: 0,
      width: SCREEN_WIDTH,
      height: undefined,
      resizeMode: 'cover',
      aspectRatio: 375 / 321,
      zIndex: 1,
    },
  });
};
